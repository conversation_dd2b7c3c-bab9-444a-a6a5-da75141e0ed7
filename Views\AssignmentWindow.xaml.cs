using System.Windows;
using DriverManagementSystem.Models;
using DriverManagementSystem.ViewModels;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// Interaction logic for AssignmentWindow.xaml
    /// </summary>
    public partial class AssignmentWindow : Window
    {
        public AssignmentWindow(FieldVisit selectedVisit)
        {
            InitializeComponent();
            
            // تعيين ViewModel مع الزيارة المحددة
            var viewModel = new AssignmentViewModel(selectedVisit);
            DataContext = viewModel;
            
            // تحديث العنوان
            Title = $"التكليف - زيارة {selectedVisit.VisitNumber}";
        }
    }
}
