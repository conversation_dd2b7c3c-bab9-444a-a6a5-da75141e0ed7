<Window x:Class="DriverManagementSystem.Views.AssignmentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="التكليف" 
        Height="800" 
        Width="1000"
        WindowStartupLocation="CenterScreen"
        Background="#F8F9FA"
        FlowDirection="RightToLeft"
        FontFamily="Arial">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="📋" FontSize="24" Margin="0,0,10,0" VerticalAlignment="Center"/>
                    <TextBlock Text="التكليف" 
                             FontSize="24" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="🖨️ طباعة"
                            Command="{Binding PrintCommand}"
                            Background="#17A2B8"
                            Foreground="White"
                            Padding="15,8"
                            FontWeight="Bold"
                            BorderThickness="0"
                            Margin="0,0,10,0">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Cursor" Value="Hand"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#138496"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>

                    <Button Content="❌ إغلاق"
                            Command="{Binding CloseCommand}"
                            CommandParameter="{Binding RelativeSource={RelativeSource AncestorType=Window}}"
                            Background="#DC3545"
                            Foreground="White"
                            Padding="15,8"
                            FontWeight="Bold"
                            BorderThickness="0">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Cursor" Value="Hand"/>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#C82333"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <Border Background="White" BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8">
                <StackPanel Margin="40,30">

                    <!-- Header Section -->
                    <Grid Margin="0,0,0,30">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="120"/>
                        </Grid.ColumnDefinitions>

                        <!-- Logo Placeholder -->
                        <Border Grid.Column="0" Background="#E9ECEF" Width="100" Height="100" 
                                CornerRadius="5" VerticalAlignment="Top">
                            <TextBlock Text="🏛️" FontSize="40" HorizontalAlignment="Center" 
                                     VerticalAlignment="Center"/>
                        </Border>

                        <!-- Organization Info -->
                        <StackPanel Grid.Column="1" HorizontalAlignment="Center" VerticalAlignment="Top">
                            <TextBlock Text="{Binding OrganizationName}" 
                                     FontSize="14" FontWeight="Bold" TextAlignment="Center"
                                     Foreground="#2C3E50" LineHeight="20"/>
                        </StackPanel>

                        <!-- Assignment Number -->
                        <StackPanel Grid.Column="2" HorizontalAlignment="Left" VerticalAlignment="Top">
                            <TextBlock Text="الموافق:" FontSize="12" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding AssignmentDate}" FontSize="12" Margin="0,0,0,10"/>
                            <TextBlock Text="التاريخ:" FontSize="12" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBlock Text="{Binding HijriDate}" FontSize="12"/>
                        </StackPanel>
                    </Grid>

                    <!-- Title -->
                    <Border Background="#2C3E50" Padding="15,10" Margin="0,0,0,30" CornerRadius="5">
                        <TextBlock Text="تكليف" FontSize="20" FontWeight="Bold" 
                                 Foreground="White" HorizontalAlignment="Center"/>
                    </Border>

                    <!-- Assignment Content -->
                    <StackPanel>
                        <!-- Assignment Text -->
                        <TextBlock FontSize="14" LineHeight="25" Margin="0,0,0,20" TextAlignment="Justify">
                            <Run Text="يكلف الصندوق الاجتماعي للتنمية - فرع ذمار البيضاء المبين اسمائهم في الجدول أدناه لتنفيذ المهمة التالية:-"/>
                        </TextBlock>

                        <!-- Mission Details -->
                        <Border BorderBrush="#CCCCCC" BorderThickness="1" Padding="15" Margin="0,0,0,20" Background="#F8F9FA">
                            <StackPanel>
                                <TextBlock Text="• المشاريع:" FontWeight="Bold" FontSize="12" Margin="0,0,0,5"/>
                                <ItemsControl ItemsSource="{Binding Projects}" Margin="20,0,0,10">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" Margin="0,2">
                                                <TextBlock Text="- " FontSize="12"/>
                                                <TextBlock Text="{Binding ProjectNumber}" FontWeight="Bold" FontSize="12" Margin="0,0,5,0"/>
                                                <TextBlock Text="{Binding ProjectName}" FontSize="12" TextWrapping="Wrap"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>

                                <TextBlock Text="• النشاط:" FontWeight="Bold" FontSize="12" Margin="0,0,0,5"/>
                                <TextBlock Text="{Binding MissionPurpose}" FontSize="12" Margin="20,0,0,10" TextWrapping="Wrap"/>

                                <TextBlock Text="• خط السير:" FontWeight="Bold" FontSize="12" Margin="0,0,0,5"/>
                                <TextBlock Text="{Binding SectorName}" FontSize="12" Margin="20,0,0,10"/>

                                <Grid Margin="0,10,0,0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <StackPanel Grid.Column="0">
                                        <TextBlock Text="• تاريخ التحرك:" FontWeight="Bold" FontSize="12" Margin="0,0,0,5"/>
                                        <TextBlock Text="{Binding DepartureDate}" FontSize="12" Margin="20,0,0,0"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="1">
                                        <TextBlock Text="• تاريخ العودة:" FontWeight="Bold" FontSize="12" Margin="0,0,0,5"/>
                                        <TextBlock Text="{Binding ReturnDate}" FontSize="12" Margin="20,0,0,0"/>
                                    </StackPanel>

                                    <StackPanel Grid.Column="2">
                                        <TextBlock Text="• عدد الأيام:" FontWeight="Bold" FontSize="12" Margin="0,0,0,5"/>
                                        <TextBlock Text="{Binding DaysCount}" FontSize="12" Margin="20,0,0,0"/>
                                    </StackPanel>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- Participants Table -->
                        <TextBlock Text="الأشخاص:" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                        
                        <Border BorderBrush="Black" BorderThickness="2" Margin="0,0,0,20">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- Table Header -->
                                <Border Grid.Row="0" Background="#2C3E50" BorderBrush="Black" BorderThickness="0,0,0,2">
                                    <Grid MinHeight="35">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="60"/>
                                            <ColumnDefinition Width="120"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="120"/>
                                        </Grid.ColumnDefinitions>

                                        <TextBlock Grid.Column="0" Text="ت" FontWeight="Bold" FontSize="12"
                                                 Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        <Border Grid.Column="0" BorderBrush="White" BorderThickness="0,0,1,0"/>

                                        <TextBlock Grid.Column="1" Text="نوعها" FontWeight="Bold" FontSize="12"
                                                 Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        <Border Grid.Column="1" BorderBrush="White" BorderThickness="0,0,1,0"/>

                                        <TextBlock Grid.Column="2" Text="رقم التقويم" FontWeight="Bold" FontSize="12"
                                                 Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        <Border Grid.Column="2" BorderBrush="White" BorderThickness="0,0,1,0"/>

                                        <TextBlock Grid.Column="3" Text="الاسم" FontWeight="Bold" FontSize="12"
                                                 Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Grid>
                                </Border>

                                <!-- Table Data -->
                                <ItemsControl Grid.Row="1" ItemsSource="{Binding Visitors}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border BorderBrush="Black" BorderThickness="0,0,0,1">
                                                <Grid MinHeight="35" Background="White">
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="60"/>
                                                        <ColumnDefinition Width="120"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="120"/>
                                                    </Grid.ColumnDefinitions>

                                                    <TextBlock Grid.Column="0" Text="{Binding Index}" FontSize="10"
                                                             HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    <Border Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0"/>

                                                    <TextBlock Grid.Column="1" Text="شخصية" FontSize="10"
                                                             HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    <Border Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0"/>

                                                    <TextBlock Grid.Column="2" Text="{Binding OfficerCode}" FontSize="10"
                                                             HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    <Border Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,1,0"/>

                                                    <TextBlock Grid.Column="3" FontSize="10" HorizontalAlignment="Center" VerticalAlignment="Center">
                                                        <Run Text="{Binding OfficerRank}"/>
                                                        <Run Text=" "/>
                                                        <Run Text="{Binding OfficerName}"/>
                                                    </TextBlock>
                                                </Grid>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </Grid>
                        </Border>

                        <!-- Footer Note -->
                        <TextBlock FontSize="12" Margin="0,20,0,0" TextAlignment="Justify">
                            <Run Text="وعليه تكرموا بالتعاون مع المذكورين لما فيه المصلحة العامة"/>
                        </TextBlock>

                        <!-- Signature Section -->
                        <Grid Margin="0,40,0,0">
                            <TextBlock Text="مدير الفرع" FontWeight="Bold" FontSize="14" 
                                     HorizontalAlignment="Left" VerticalAlignment="Bottom"/>
                            <TextBlock Text="م/محمد محمد الديلمي" FontSize="12" 
                                     HorizontalAlignment="Left" VerticalAlignment="Bottom" Margin="0,20,0,0"/>
                        </Grid>
                    </StackPanel>
                </StackPanel>
            </Border>
        </ScrollViewer>

        <!-- Footer -->
        <Border Grid.Row="2" Background="#E9ECEF" Padding="15,10" BorderBrush="#CCCCCC" BorderThickness="0,1,0,0">
            <TextBlock Text="الصندوق الاجتماعي للتنمية - فرع ذمار والبيضاء" 
                     FontSize="10" HorizontalAlignment="Center" Foreground="#666666"/>
        </Border>
    </Grid>
</Window>
