using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using Microsoft.Win32;
using iTextSharp.text;
using iTextSharp.text.pdf;
using System.Diagnostics;
using System.Windows.Xps.Packaging;
using System.Windows.Xps;
using System.Printing;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Models;
using PdfParagraph = iTextSharp.text.Paragraph;

namespace DriverManagementSystem.Views
{
    public partial class ReportWindow : Window
    {
        // A4 dimensions in DIP (Device Independent Pixels) at 96 DPI
        private const double A4WidthDip = 793.7;  // 21.0 cm
        private const double A4HeightDip = 1122.52; // 29.7 cm
        private const double MarginDip = 75.59;   // 2.0 cm margins

        public ReportWindow()
        {
            InitializeComponent();
            DataContext = new ReportViewModel();
        }

        public ReportWindow(FieldVisit selectedVisit)
        {
            InitializeComponent();
            var viewModel = new ReportViewModel();

            // تحميل بيانات الزيارة المحددة أولاً
            System.Diagnostics.Debug.WriteLine($"🎯 ReportWindow: تحميل بيانات الزيارة {selectedVisit.VisitNumber}");
            viewModel.SetSelectedVisit(selectedVisit);

            // تعيين DataContext بعد تحميل البيانات
            DataContext = viewModel;

            // إجبار تحديث الواجهة
            this.UpdateLayout();
        }

        private void GenerateReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("سيتم تطبيق إنشاء التقرير قريباً", "قيد التطوير",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ShowPrintPreview();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PrintCurrentReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                PrintCurrentReportDirectly();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportPdfButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ExportToPdf();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير PDF: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void MessageDocumentationButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على الزيارة المحددة من ViewModel
                var viewModel = DataContext as ReportViewModel;
                var selectedVisit = viewModel?.SelectedVisit;

                if (selectedVisit != null)
                {
                    // فتح نافذة توثيق الرسائل مع الزيارة المحددة
                    var messageWindow = new PowerfulMessageDocumentationWindow(selectedVisit);
                    messageWindow.ShowDialog();
                }
                else
                {
                    // فتح نافذة توثيق الرسائل العامة
                    var messageWindow = new PowerfulMessageDocumentationWindow();
                    messageWindow.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة توثيق الرسائل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AssignmentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على الزيارة المحددة من ViewModel
                var viewModel = DataContext as ReportViewModel;
                var selectedVisit = viewModel?.SelectedVisit;

                if (selectedVisit != null)
                {
                    // فتح نافذة التكليف مع الزيارة المحددة
                    var assignmentWindow = new AssignmentWindow(selectedVisit);
                    assignmentWindow.ShowDialog();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار زيارة أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة التكليف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// زر الطباعة الجديد المتطور - معاينة احترافية مثل Microsoft Print
        /// </summary>
        private void NewAdvancedPrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("بدء اختبار الطباعة المتطورة...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);

                // إنشاء صفحة تجريبية بسيطة لتجنب أي مشاكل
                MessageBox.Show("إنشاء صفحات تجريبية...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);

                var samplePages = CreateSamplePages();

                if (samplePages == null)
                {
                    MessageBox.Show("خطأ: CreateSamplePages أرجع null", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                if (samplePages.Count == 0)
                {
                    MessageBox.Show("خطأ: CreateSamplePages أرجع قائمة فارغة", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                MessageBox.Show($"تم إنشاء {samplePages.Count} صفحة تجريبية بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                // عرض معاينة الطباعة المتطورة مباشرة
                MessageBox.Show("سيتم الآن إنشاء نافذة المعاينة...", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);

                var professionalPreview = new ProfessionalPrintPreviewWindow(samplePages);

                MessageBox.Show("تم إنشاء النافذة بنجاح، سيتم عرضها الآن...", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                professionalPreview.ShowDialog();

                MessageBox.Show("تم إكمال عملية الطباعة المتطورة بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة المتطورة:\n\n{ex.Message}\n\nStack Trace:\n{ex.StackTrace}", "خطأ مفصل",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// جمع كل الصفحات القابلة للطباعة - طريقة مبسطة
        /// </summary>
        private List<FrameworkElement> CollectAllPrintablePages()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 جمع الصفحات القابلة للطباعة...");

                var allPages = new List<FrameworkElement>();

                // 1. البحث عن ReportView (المحتوى الرئيسي)
                System.Diagnostics.Debug.WriteLine("🔍 البحث عن ReportView...");
                var reportView = FindReportView();
                if (reportView != null)
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم العثور على ReportView");
                    allPages.Add(reportView);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على ReportView، البحث في ScrollViewer...");
                    // إذا لم نجد ReportView، ابحث عن المحتوى في ScrollViewer
                    var scrollViewer = FindVisualChild<ScrollViewer>(this);
                    if (scrollViewer?.Content is FrameworkElement content)
                    {
                        System.Diagnostics.Debug.WriteLine("✅ تم العثور على محتوى ScrollViewer");
                        allPages.Add(content);
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على محتوى في ScrollViewer");
                    }
                }

                // 2. البحث عن صفحات العقود في التابات (إذا وجدت)
                System.Diagnostics.Debug.WriteLine("🔍 البحث عن صفحات العقود...");
                var contractPages = FindAllContractPages();
                if (contractPages != null && contractPages.Count > 0)
                {
                    allPages.AddRange(contractPages);
                    System.Diagnostics.Debug.WriteLine($"✅ تمت إضافة {contractPages.Count} صفحة عقد");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على صفحات عقود");
                }

                // 3. إذا لم نجد أي محتوى، أنشئ صفحة تجريبية
                if (allPages.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على محتوى، إنشاء صفحة تجريبية...");
                    allPages.Add(CreateSamplePage());
                }

                System.Diagnostics.Debug.WriteLine($"🎯 إجمالي الصفحات المجمعة: {allPages.Count}");
                return allPages;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جمع الصفحات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ Stack Trace: {ex.StackTrace}");
                // إرجاع صفحة تجريبية في حالة الخطأ
                return new List<FrameworkElement> { CreateSamplePage() };
            }
        }

        /// <summary>
        /// إنشاء صفحة تجريبية للاختبار
        /// </summary>
        private FrameworkElement CreateSamplePage()
        {
            var samplePage = new StackPanel
            {
                Width = A4WidthDip - (2 * MarginDip),
                Height = A4HeightDip - (2 * MarginDip),
                Background = Brushes.White,
                Margin = new Thickness(20)
            };

            // عنوان
            var title = new TextBlock
            {
                Text = "تقرير الزيارة الميدانية",
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 20, 0, 30),
                Foreground = Brushes.DarkBlue
            };

            // محتوى تجريبي
            var content = new TextBlock
            {
                Text = "هذه صفحة تجريبية لاختبار نظام الطباعة المتطور.\n\n" +
                       "المحتوى الفعلي سيظهر هنا عند توفر البيانات.\n\n" +
                       "تاريخ الإنشاء: " + DateTime.Now.ToString("yyyy/MM/dd HH:mm"),
                FontSize = 14,
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(20),
                LineHeight = 25
            };

            samplePage.Children.Add(title);
            samplePage.Children.Add(content);

            System.Diagnostics.Debug.WriteLine("✅ تم إنشاء صفحة تجريبية");
            return samplePage;
        }

        /// <summary>
        /// جمع الصفحات بالطريقة التقليدية كبديل
        /// </summary>
        private List<FrameworkElement> CollectPagesTraditionalWay()
        {
            var allPages = new List<FrameworkElement>();
            
            try
            {
                // 1. البحث عن صفحة التقرير
                var reportView = FindReportView();
                if (reportView != null)
                {
                    allPages.Add(reportView);
                    System.Diagnostics.Debug.WriteLine("✅ تمت إضافة صفحة التقرير");
                }

                // 2. البحث عن صفحات العقود في التابات
                var contractPages = FindAllContractPages();
                if (contractPages != null && contractPages.Count > 0)
                {
                    allPages.AddRange(contractPages);
                    System.Diagnostics.Debug.WriteLine($"✅ تمت إضافة {contractPages.Count} صفحة عقد");
                }

                return allPages;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في الطريقة التقليدية: {ex.Message}");
                return new List<FrameworkElement>();
            }
        }

        /// <summary>
        /// البحث عن الحاوية الرئيسية للمحتوى
        /// </summary>
        private FrameworkElement FindMainContainer()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔍 البحث عن الحاوية الرئيسية...");

                // البحث عن ReportView أولاً (هذا هو المحتوى الرئيسي)
                var reportView = FindVisualChild<ReportView>(this);
                if (reportView != null)
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم العثور على ReportView");
                    return reportView;
                }

                // البحث في ScrollViewer
                var scrollViewer = FindVisualChild<ScrollViewer>(this);
                if (scrollViewer?.Content is Border border && border.Child is FrameworkElement borderChild)
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم العثور على محتوى ScrollViewer داخل Border");
                    return borderChild;
                }
                else if (scrollViewer?.Content is FrameworkElement scrollContent)
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم العثور على محتوى ScrollViewer مباشرة");
                    return scrollContent;
                }

                // البحث في TabControl المحدد
                var tabControl = FindVisualChild<TabControl>(this);
                if (tabControl?.SelectedContent is FrameworkElement selectedContent)
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم العثور على محتوى التاب المحدد");
                    return selectedContent;
                }

                // البحث عن أي Grid يحتوي على محتوى
                var grids = FindAllVisualChildren<Grid>(this);
                foreach (var grid in grids)
                {
                    if (grid.Children.Count > 0 && grid.Name != "LayoutRoot")
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ تم العثور على Grid مع {grid.Children.Count} عنصر");
                        return grid;
                    }
                }

                System.Diagnostics.Debug.WriteLine("❌ لم يتم العثور على حاوية مناسبة");
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في البحث عن الحاوية الرئيسية: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// البحث عن جميع العناصر من نوع معين
        /// </summary>
        private static List<T> FindAllVisualChildren<T>(DependencyObject parent) where T : DependencyObject
        {
            var children = new List<T>();

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                if (child is T)
                    children.Add((T)child);

                children.AddRange(FindAllVisualChildren<T>(child));
            }

            return children;
        }

        /// <summary>
        /// فئة تقسيم الصفحات الذكية
        /// </summary>
        public static class PageSplitter
        {
            private const double A4_WIDTH_DIP = 793.7; // A4 width in DIP
            private const double A4_HEIGHT_DIP = 1122.52; // A4 height in DIP
            private const double MARGIN_DIP = 75.59; // 2cm margins
            private const double USABLE_HEIGHT = A4_HEIGHT_DIP - (2 * MARGIN_DIP);

            /// <summary>
            /// استخراج الصفحات من الحاوية الرئيسية
            /// </summary>
            public static List<FrameworkElement> ExtractPages(FrameworkElement container)
            {
                var pages = new List<FrameworkElement>();

                try
                {
                    System.Diagnostics.Debug.WriteLine($"🔍 تحليل الحاوية: {container.GetType().Name}");

                    // إذا كانت StackPanel، قسم حسب العناصر
                    if (container is StackPanel stackPanel)
                    {
                        pages.AddRange(SplitStackPanel(stackPanel));
                    }
                    // إذا كانت Grid، قسم حسب الصفوف أو العناصر
                    else if (container is Grid grid)
                    {
                        pages.AddRange(SplitGrid(grid));
                    }
                    // إذا كانت Border أو أي حاوية أخرى
                    else if (container is Panel panel)
                    {
                        pages.AddRange(SplitPanel(panel));
                    }
                    // عنصر واحد، قسم حسب الارتفاع
                    else
                    {
                        pages.AddRange(SplitByHeight(container));
                    }

                    System.Diagnostics.Debug.WriteLine($"✅ تم استخراج {pages.Count} صفحة من الحاوية");
                    return pages;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في استخراج الصفحات: {ex.Message}");
                    return new List<FrameworkElement> { container }; // إرجاع العنصر الأصلي كصفحة واحدة
                }
            }
            /// <summary>
            /// تقسيم StackPanel إلى صفحات
            /// </summary>
            private static List<FrameworkElement> SplitStackPanel(StackPanel stackPanel)
            {
                var pages = new List<FrameworkElement>();
                var currentPageElements = new List<FrameworkElement>();
                double currentHeight = 0;

                foreach (FrameworkElement child in stackPanel.Children)
                {
                    child.UpdateLayout();
                    var elementHeight = Math.Max(child.ActualHeight, child.DesiredSize.Height);

                    // إذا كان العنصر أكبر من صفحة واحدة، اجعله صفحة منفصلة
                    if (elementHeight > USABLE_HEIGHT * 0.8)
                    {
                        // إنهاء الصفحة الحالية إذا كانت تحتوي على عناصر
                        if (currentPageElements.Count > 0)
                        {
                            pages.Add(CreatePageFromElements(currentPageElements));
                            currentPageElements.Clear();
                            currentHeight = 0;
                        }

                        // إضافة العنصر الكبير كصفحة منفصلة
                        pages.Add(CreateSingleElementPage(child));
                    }
                    // إذا كان العنصر يتسع في الصفحة الحالية
                    else if (currentHeight + elementHeight <= USABLE_HEIGHT)
                    {
                        currentPageElements.Add(child);
                        currentHeight += elementHeight;
                    }
                    // إذا كان العنصر لا يتسع، ابدأ صفحة جديدة
                    else
                    {
                        if (currentPageElements.Count > 0)
                        {
                            pages.Add(CreatePageFromElements(currentPageElements));
                            currentPageElements.Clear();
                        }

                        currentPageElements.Add(child);
                        currentHeight = elementHeight;
                    }
                }

                // إضافة الصفحة الأخيرة
                if (currentPageElements.Count > 0)
                {
                    pages.Add(CreatePageFromElements(currentPageElements));
                }

                return pages;
            }

            /// <summary>
            /// تقسيم Grid إلى صفحات
            /// </summary>
            private static List<FrameworkElement> SplitGrid(Grid grid)
            {
                var pages = new List<FrameworkElement>();

                // إذا كان Grid يحتوي على صفوف، قسم حسب الصفوف
                if (grid.RowDefinitions.Count > 1)
                {
                    for (int row = 0; row < grid.RowDefinitions.Count; row++)
                    {
                        var rowElements = grid.Children.OfType<FrameworkElement>()
                            .Where(child => Grid.GetRow(child) == row).ToList();

                        if (rowElements.Count > 0)
                        {
                            pages.Add(CreatePageFromElements(rowElements));
                        }
                    }
                }
                else
                {
                    // إذا لم يكن هناك صفوف، قسم حسب العناصر
                    pages.AddRange(SplitByHeight(grid));
                }

                return pages;
            }

            /// <summary>
            /// تقسيم Panel عام
            /// </summary>
            private static List<FrameworkElement> SplitPanel(Panel panel)
            {
                var pages = new List<FrameworkElement>();

                foreach (FrameworkElement child in panel.Children)
                {
                    pages.Add(CreateSingleElementPage(child));
                }

                return pages;
            }

            /// <summary>
            /// تقسيم حسب الارتفاع
            /// </summary>
            private static List<FrameworkElement> SplitByHeight(FrameworkElement element)
            {
                element.UpdateLayout();
                var totalHeight = Math.Max(element.ActualHeight, element.DesiredSize.Height);

                // إذا كان العنصر أصغر من صفحة واحدة
                if (totalHeight <= USABLE_HEIGHT)
                {
                    return new List<FrameworkElement> { CreateSingleElementPage(element) };
                }

                // إذا كان العنصر أكبر، قسمه إلى أجزاء
                var pages = new List<FrameworkElement>();
                var numberOfPages = Math.Ceiling(totalHeight / USABLE_HEIGHT);

                for (int i = 0; i < numberOfPages; i++)
                {
                    var pageElement = CreatePageSection(element, i, (int)numberOfPages);
                    pages.Add(pageElement);
                }

                return pages;
            }

            /// <summary>
            /// إنشاء صفحة من مجموعة عناصر
            /// </summary>
            private static FrameworkElement CreatePageFromElements(List<FrameworkElement> elements)
            {
                var pageContainer = new StackPanel
                {
                    Orientation = Orientation.Vertical,
                    Width = A4_WIDTH_DIP - (2 * MARGIN_DIP),
                    Background = Brushes.White
                };

                foreach (var element in elements)
                {
                    // إنشاء نسخة من العنصر
                    var elementCopy = CreateElementCopy(element);
                    pageContainer.Children.Add(elementCopy);
                }

                return pageContainer;
            }

            /// <summary>
            /// إنشاء صفحة من عنصر واحد
            /// </summary>
            private static FrameworkElement CreateSingleElementPage(FrameworkElement element)
            {
                var pageContainer = new Grid
                {
                    Width = A4_WIDTH_DIP - (2 * MARGIN_DIP),
                    Height = USABLE_HEIGHT,
                    Background = Brushes.White
                };

                var elementCopy = CreateElementCopy(element);
                pageContainer.Children.Add(elementCopy);

                return pageContainer;
            }

            /// <summary>
            /// إنشاء قسم من صفحة كبيرة
            /// </summary>
            private static FrameworkElement CreatePageSection(FrameworkElement element, int sectionIndex, int totalSections)
            {
                var pageContainer = new Grid
                {
                    Width = A4_WIDTH_DIP - (2 * MARGIN_DIP),
                    Height = USABLE_HEIGHT,
                    Background = Brushes.White,
                    ClipToBounds = true
                };

                var elementCopy = CreateElementCopy(element);

                // تحديد موضع القسم
                var offsetY = -sectionIndex * USABLE_HEIGHT;
                elementCopy.RenderTransform = new TranslateTransform(0, offsetY);

                pageContainer.Children.Add(elementCopy);

                return pageContainer;
            }

            /// <summary>
            /// إنشاء نسخة من العنصر
            /// </summary>
            private static FrameworkElement CreateElementCopy(FrameworkElement original)
            {
                try
                {
                    original.UpdateLayout();

                    var width = Math.Max(original.ActualWidth, 400);
                    var height = Math.Max(original.ActualHeight, 100);

                    var renderBitmap = new RenderTargetBitmap(
                        (int)width, (int)height, 96, 96, PixelFormats.Pbgra32);

                    renderBitmap.Render(original);

                    return new System.Windows.Controls.Image
                    {
                        Source = renderBitmap,
                        Stretch = Stretch.Uniform,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Top
                    };
                }
                catch
                {
                    return new TextBlock
                    {
                        Text = "محتوى الصفحة",
                        FontSize = 16,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Center
                    };
                }
            }
        }

        /// <summary>
        /// البحث عن جميع صفحات العقود
        /// </summary>
        private List<FrameworkElement> FindAllContractPages()
        {
            var contractPages = new List<FrameworkElement>();

            try
            {
                // البحث في TabControl
                var tabControl = FindVisualChild<TabControl>(this);
                if (tabControl != null)
                {
                    foreach (TabItem tab in tabControl.Items)
                    {
                        var header = tab.Header?.ToString() ?? "";
                        if (header.Contains("العقد") || header.Contains("Contract") ||
                            header.Contains("صفحة") || header.Contains("Page"))
                        {
                            if (tab.Content is FrameworkElement content)
                            {
                                contractPages.Add(content);
                                System.Diagnostics.Debug.WriteLine($"✅ تمت إضافة صفحة: {header}");
                            }
                        }
                    }
                }

                return contractPages;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في البحث عن صفحات العقود: {ex.Message}");
                return new List<FrameworkElement>();
            }
        }

        /// <summary>
        /// إنشاء وثيقة طباعة متطورة مع كل الصفحات
        /// </summary>
        private FixedDocument CreateAdvancedPrintDocument(List<FrameworkElement> pages)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"📄 إنشاء وثيقة طباعة متطورة مع {pages.Count} صفحة...");

                var document = new FixedDocument();
                document.DocumentPaginator.PageSize = new Size(A4WidthDip, A4HeightDip);

                for (int i = 0; i < pages.Count; i++)
                {
                    var page = pages[i];
                    System.Diagnostics.Debug.WriteLine($"📄 معالجة الصفحة {i + 1} من {pages.Count}");

                    // إنشاء صفحة A4 مع المحتوى
                    var fixedPage = CreateA4PageWithContent(page, i + 1, pages.Count);

                    var pageContent = new PageContent();
                    pageContent.Child = fixedPage;
                    document.Pages.Add(pageContent);
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء وثيقة متطورة مع {document.Pages.Count} صفحة");
                return document;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء الوثيقة المتطورة: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء صفحة A4 مع المحتوى ورقم الصفحة
        /// </summary>
        private FixedPage CreateA4PageWithContent(FrameworkElement content, int pageNumber, int totalPages)
        {
            try
            {
                var fixedPage = new FixedPage
                {
                    Width = A4WidthDip,
                    Height = A4HeightDip,
                    Background = Brushes.White
                };

                // إنشاء نسخة من المحتوى
                var contentCopy = CreateHighQualityCopy(content);

                // حاوية رئيسية مع هوامش محسنة لتجنب الاقتصاص
                var mainContainer = new Grid
                {
                    Width = A4WidthDip - (MarginDip * 0.4), // تقليل العرض قليلاً لتجنب الاقتصاص
                    Height = A4HeightDip - (MarginDip * 0.4), // تقليل الارتفاع قليلاً لتجنب الاقتصاص
                    Margin = new Thickness(MarginDip * 0.2) // هوامش صغيرة ومتوازنة
                };

                // إضافة المحتوى
                mainContainer.Children.Add(contentCopy);

                // إضافة رقم الصفحة في أسفل اليسار وأرفعه قليلاً
                var pageNumberContainer = new Border
                {
                    Background = new SolidColorBrush(Color.FromArgb(240, 255, 255, 255)), // خلفية شبه شفافة
                    BorderBrush = new SolidColorBrush(Color.FromArgb(100, 128, 128, 128)), // حدود رمادية خفيفة
                    BorderThickness = new Thickness(1, 1, 1, 1),
                    CornerRadius = new CornerRadius(3),
                    Padding = new Thickness(8, 4, 8, 4),
                    HorizontalAlignment = HorizontalAlignment.Left, // تغيير إلى اليسار
                    VerticalAlignment = VerticalAlignment.Bottom,
                    Margin = new Thickness(5, 0, 0, 25), // يسار 5، أسفل 25 لرفعه قليلاً
                    Child = new TextBlock
                    {
                        Text = pageNumber.ToString(), // رقم الصفحة فقط بشكل احترافي
                        FontSize = 11,
                        FontWeight = FontWeights.SemiBold,
                        Foreground = new SolidColorBrush(Color.FromRgb(64, 64, 64)), // رمادي داكن
                        TextAlignment = TextAlignment.Center
                    }
                };
                mainContainer.Children.Add(pageNumberContainer);

                fixedPage.Children.Add(mainContainer);
                return fixedPage;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء صفحة A4: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء نسخة عالية الجودة من المحتوى
        /// </summary>
        private FrameworkElement CreateHighQualityCopy(FrameworkElement original)
        {
            try
            {
                // التأكد من أن العنصر محدث
                original.UpdateLayout();

                // إنشاء صورة بالحجم الطبيعي - الاعتماد على إعدادات الطابعة
                var width = Math.Max(original.ActualWidth, A4WidthDip - MarginDip * 0.3);
                var height = Math.Max(original.ActualHeight, A4HeightDip - MarginDip * 0.3);

                // إنشاء RenderTargetBitmap بالحجم الطبيعي
                var renderBitmap = new RenderTargetBitmap(
                    (int)width, (int)height, 96, 96, PixelFormats.Pbgra32); // DPI طبيعي

                renderBitmap.Render(original);

                // إنشاء Image مع الصورة - ملء الصفحة بالكامل
                var imageElement = new System.Windows.Controls.Image
                {
                    Source = renderBitmap,
                    Stretch = Stretch.Fill, // ملء الصفحة بالكامل بدلاً من Uniform
                    HorizontalAlignment = HorizontalAlignment.Stretch,
                    VerticalAlignment = VerticalAlignment.Stretch
                };

                return imageElement;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء نسخة عالية الجودة: {ex.Message}");

                // في حالة الخطأ، إرجاع نص بديل
                return new TextBlock
                {
                    Text = "خطأ في عرض المحتوى",
                    FontSize = 16,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center
                };
            }
        }

        /// <summary>
        /// عرض معاينة الطباعة الاحترافية مثل Microsoft Office
        /// </summary>
        private void ShowAdvancedPrintPreview(FixedDocument document)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🖼️ عرض معاينة الطباعة الاحترافية...");

                // جمع الصفحات للمعاينة الاحترافية
                var pages = CollectAllPrintablePages();

                if (pages == null || pages.Count == 0)
                {
                    // إنشاء صفحة تجريبية إذا لم توجد صفحات
                    pages = CreateSamplePages();

                    if (pages.Count == 0)
                    {
                        MessageBox.Show("لا يوجد محتوى للمعاينة", "تنبيه",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                }

                // إنشاء وعرض النافذة الاحترافية
                var professionalPreview = new ProfessionalPrintPreviewWindow(pages);
                professionalPreview.ShowDialog();

                System.Diagnostics.Debug.WriteLine("✅ تم عرض معاينة الطباعة الاحترافية بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في عرض معاينة الطباعة: {ex.Message}");
                MessageBox.Show($"خطأ في عرض المعاينة: {ex.Message}\n\nتفاصيل: {ex.StackTrace}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        private void PrintCurrentReportDirectly()
        {
            // Implementation for direct printing
            MessageBox.Show("سيتم تطبيق الطباعة المباشرة قريباً", "قيد التطوير",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ShowPrintPreview()
        {
            // Implementation for print preview
            MessageBox.Show("سيتم تطبيق معاينة الطباعة قريباً", "قيد التطوير",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExportToPdf()
        {
            // Implementation for PDF export
            MessageBox.Show("سيتم تطبيق تصدير PDF قريباً", "قيد التطوير",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private ReportView FindReportView()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔍 البحث عن ReportView في الشجرة المرئية...");
                // Find the ReportView in the visual tree
                var reportView = FindVisualChild<ReportView>(this);
                if (reportView != null)
                {
                    System.Diagnostics.Debug.WriteLine("✅ تم العثور على ReportView");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على ReportView");
                }
                return reportView;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في البحث عن ReportView: {ex.Message}");
                return null;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// إنشاء صفحات تجريبية للمعاينة
        /// </summary>
        private List<FrameworkElement> CreateSamplePages()
        {
            var pages = new List<FrameworkElement>();

            try
            {
                // إنشاء صفحة تجريبية بسيطة
                var samplePage = new Border
                {
                    Width = 793.7, // A4 width
                    Height = 1122.52, // A4 height
                    Background = Brushes.White,
                    BorderBrush = Brushes.Gray,
                    BorderThickness = new Thickness(1),
                    Child = new StackPanel
                    {
                        Margin = new Thickness(50),
                        Children =
                        {
                            new TextBlock
                            {
                                Text = "تقرير الزيارة الميدانية",
                                FontSize = 24,
                                FontWeight = FontWeights.Bold,
                                HorizontalAlignment = HorizontalAlignment.Center,
                                Margin = new Thickness(0, 0, 0, 30)
                            },
                            new TextBlock
                            {
                                Text = "لا يوجد محتوى متاح للعرض حالياً",
                                FontSize = 16,
                                HorizontalAlignment = HorizontalAlignment.Center,
                                Margin = new Thickness(0, 0, 0, 20)
                            },
                            new TextBlock
                            {
                                Text = "يرجى إنشاء التقرير أولاً من خلال اختيار زيارة ميدانية",
                                FontSize = 14,
                                HorizontalAlignment = HorizontalAlignment.Center,
                                TextWrapping = TextWrapping.Wrap
                            }
                        }
                    }
                };

                pages.Add(samplePage);
                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء صفحة تجريبية");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء الصفحة التجريبية: {ex.Message}");
            }

            return pages;
        }

        private static T FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            try
            {
                if (parent == null)
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ Parent is null in FindVisualChild");
                    return null;
                }

                int childrenCount = VisualTreeHelper.GetChildrenCount(parent);
                System.Diagnostics.Debug.WriteLine($"🔍 البحث في {childrenCount} عنصر فرعي من نوع {parent.GetType().Name}");

                for (int i = 0; i < childrenCount; i++)
                {
                    DependencyObject child = VisualTreeHelper.GetChild(parent, i);
                    if (child == null) continue;

                    System.Diagnostics.Debug.WriteLine($"  - فحص العنصر {i}: {child.GetType().Name}");

                    if (child is T foundChild)
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ تم العثور على {typeof(T).Name}!");
                        return foundChild;
                    }

                    T childOfChild = FindVisualChild<T>(child);
                    if (childOfChild != null)
                        return childOfChild;
                }

                System.Diagnostics.Debug.WriteLine($"⚠️ لم يتم العثور على {typeof(T).Name} في {parent.GetType().Name}");
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في FindVisualChild: {ex.Message}");
                return null;
            }
        }
    }
}
